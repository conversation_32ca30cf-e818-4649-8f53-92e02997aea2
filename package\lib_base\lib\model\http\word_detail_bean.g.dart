// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word_detail_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WordDetailBean _$WordDetailBeanFromJson(Map<String, dynamic> json) =>
    WordDetailBean(
      id: json['id'] as String?,
      name: json['name'] as String?,
      paraphrase: json['paraphrase'] as String?,
      audioFile: json['audioFile'] as String?,
      wordId: json['wordId'] as String?,
      meaningNames: json['meaningNames'] as String?,
      filePath: json['filePath'] as String?,
      sort: json['sort'] as num?,
      appStatus: json['appStatus'] as num?,
      isShow: json['isShow'] as String?,
      sourceType: json['sourceType'] as String?,
      releaseState: json['releaseState'] as String?,
    );

Map<String, dynamic> _$WordDetailBeanToJson(WordDetailBean instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'paraphrase': instance.paraphrase,
      'audioFile': instance.audioFile,
      'wordId': instance.wordId,
      'meaningNames': instance.meaningNames,
      'filePath': instance.filePath,
      'sort': instance.sort,
      'appStatus': instance.appStatus,
      'isShow': instance.isShow,
      'sourceType': instance.sourceType,
      'releaseState': instance.releaseState,
    };
