import 'package:json_annotation/json_annotation.dart';
import 'word_detail_bean.dart';

part 'collect_word_bean.g.dart';

@JsonSerializable()
class CollectWordBean {
  String? id;
  String? expModuleId;
  String? type;
  WordDetailBean? obj;

  CollectWordBean({
    this.id,
    this.expModuleId,
    this.type,
    this.obj,
  });

  factory CollectWordBean.fromJson(Map<String, dynamic> json) =>
      _$CollectWordBeanFromJson(json);

  Map<String, dynamic> toJson() => _$CollectWordBeanToJson(this);
}
