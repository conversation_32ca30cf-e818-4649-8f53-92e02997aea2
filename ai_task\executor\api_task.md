
 
 Java接口定义:

```
        /**
     * 用户查询单词收藏
     *
     * @return
     */
    @Headers({"url_name:newhost"})
    @GET("/studymodules-api/api/yyb/v1/expModule/api/findWordListByUserId")
    Call<JSONObject> findWordListByUserId(@Query("pageNo") int pageIndex, @Query("pageSize") int pageSize);

```

  目标模块名: 非子模块,  lib路径下, 

 anroid response model : response model 是 BasePageListResponseModel(这个model在项目中已经存在了) , 里面的list的元素对应的android 代码 ai_task\cache\CollectWordBean.java,   
 CollectWordBean的 obj对应的android model: ai_task\cache\WordDetailBean.java
 
  

 

请按Flutter模块API接口添加任务模板 里面的描述执行任务




# Flutter模块API接口添加任务模板（优化版）

## 🚀 快速执行模式
**执行时间：30秒以内**
1. **参数解析**（5秒）：提取模块名、接口信息、Model信息
2. **批量生成**（20秒）：一次性生成所有文件和代码
3. **统一验证**（5秒）：最终验证所有文件

## 使用方式
只需提供以下参数即可执行任务：

### 必需参数
1. **目标模块名**：如 `lib_base`、`yyb_auth`、`yyb_basic_training` 等
2. **Java接口定义**：完整的Java接口定义代码

### 可选参数
3. **Android Response Model**：如果提供，将自动生成对应的Dart model并添加到Response泛型中，同时生成对应的 .g.dart 文件

### 使用示例

#### 基础使用（无model）
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：lib_base
接口定义：
/**
 * 根据一级单元id查询视频资源
 */
@Headers({"url_name:newhost"})
@GET("/api/math/v50/mathknowledgecontrollerapi/selectvideoresourcelist")
Call<JSONObject> selectvideoresourcelist(@Query("bookId") String bookId, @Query("oneUnitId") String oneUnitId, @Query("userId") String userId);
```

#### 高级使用（包含model）
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：yyb_auth
接口定义：
/**
 * 用户登录接口
 */
@Headers({"url_name:newhost"})
@POST("/api/auth/v1/login")
Call<LoginResponse> userLogin(@Field("username") String username, @Field("password") String password);

Android Response Model：
public class LoginResponse {
    private String token;
    private String userId;
    private long expireTime;

    // getters and setters...
}

注意：执行后将自动生成 login_response.dart 和 login_response.g.dart 两个文件，无需手动运行 build_runner
```

---

## 🎯 优化执行策略

### 1. 预缓存模板库（减少信息收集时间）
**lib_base模块预设格式**：
- API常量：`static const String {接口名} = "{API路径}";`
- 接口定义：`@GET(BaseApi.{接口名}) Future<BaseResponse<{Model}>> {接口名}(...);`
- Repository：`static Future<BaseResponse<{Model}>> {接口名}(...) { return BaseAppApi.instance().{接口名}(...); }`

**其他模块预设格式**：
- API常量：`static const String {接口名} = "{API路径}";`
- 接口定义：`@GET(Api.{接口名}) Future<BaseResponse<{Model}>> {接口名}(...);`
- Repository：`static Future<BaseResponse<{Model}>> {接口名}(...) { return AppApi.instance().{接口名}(...); }`

### 2. 智能路径识别（无需查看文件结构）
- **lib_base模块** → `package\lib_base\lib\api` + BaseApi/BaseAppApi
- **其他模块** → `package\{模块名}\lib\src\api` + Api/AppApi

### 3. 批量代码生成（一次性完成所有文件）
**执行顺序优化**：
1. 同时解析Java接口和Android Model
2. 并行生成所有代码片段
3. 批量写入所有文件
4. 统一验证结果

## 📋 快速代码生成模板

### 模板1：Dart Model生成（可选，仅当提供Android Model时）
**生成路径**：`package\{模块名}\lib\model\http\{model_name}.dart` + `.g.dart`

**主文件模板**：
```dart
import 'package:json_annotation/json_annotation.dart';
// 自动添加必要的导入

part '{model_name}.g.dart';

@JsonSerializable()
class {ModelName} {
  // 自动生成所有字段（可空类型，数字用num?）
  const {ModelName}({...});
  factory {ModelName}.fromJson(Map<String, dynamic> json) => _${ModelName}FromJson(json);
  Map<String, dynamic> toJson() => _${ModelName}ToJson(this);
}
```

**.g.dart文件模板**：
```dart
// GENERATED CODE - DO NOT MODIFY BY HAND
part of '{model_name}.dart';
// 自动生成fromJson和toJson实现
```

### 模板2：API常量（api.dart末尾添加）
```dart
/**
 * {从Java注释提取的描述}
 * @param {参数名} {参数描述}
 * @return
 */
static const String {接口名} = "{API路径}";
```

### 模板3：接口定义（app_api.dart 末尾添加）
**导入添加**（如果有model）：
```dart
import 'package:{模块名}/model/http/{model_name}.dart';
```

**接口方法**（末尾添加）：
```dart
/**
 * {从Java注释提取的描述}
 */
@{GET/POST}({BaseApi/Api}.{接口名})  // 根据模块自动选择
@Extra(<String, bool>{
  HttpExtraKey.autoLoading: false,
  HttpExtraKey.withoutLogin: false,
  HttpExtraKey.needErrorToast: true,
})
Future<BaseResponse<{ModelName/dynamic}>> {接口名}(  // 有model用ModelName，无model用dynamic
  @{Query/Field}("{参数名}") String {参数名},  // GET用Query，POST用Field
);
```

### 模板4：app_api.g.dart更新（自动生成实现）
```dart
@override
Future<BaseResponse<{ModelName/dynamic}>> {接口名}(...) async {
  // 自动生成完整的HTTP请求实现
  final value = BaseResponse<{ModelName/dynamic}>.fromJson(
    _result.data!,
    (json) => {ModelName.fromJson(json as Map<String, dynamic>) / json as dynamic},
  );
  return value;
}
```

### 模板5：Repository方法（api_repository.dart末尾添加）
**导入添加**（如果有model）：
```dart
import 'package:{模块名}/model/http/{model_name}.dart';
```

**Repository方法**：
```dart
/**
 * {从Java注释提取的描述}
 */
static Future<BaseResponse<{ModelName/dynamic}>> {接口名}({
  required String {参数名},
}) {
  return {BaseAppApi/AppApi}.instance().{接口名}({参数列表});  // 根据模块选择
}
```

---

## ⚡ 高效执行指令

### 🎯 执行策略（30秒完成）
**不要创建子任务，不要逐步执行，直接批量完成所有操作！**

1. **快速解析**（5秒）：
   - 提取接口名、请求类型、API路径、参数列表
   - 判断模块类型（lib_base vs 其他）
   - 解析Android Model（如果有）

2. **批量生成**（20秒）：
   - 同时生成所有需要的代码片段
   - 一次性创建/修改所有文件
   - 使用预设模板，无需查看现有文件结构

3. **统一验证**（5秒）：
   - 最后统一检查所有文件
   - 确保没有语法错误

### 🚀 智能规则（无需查看文件）
- **lib_base** → BaseAppApi + BaseApi
- **其他模块** → AppApi + Api
- **@GET** → @Query参数
- **@POST** → @Field参数
- **有Model** → BaseResponse<ModelName>
- **无Model** → BaseResponse<dynamic>

### 📝 Java到Dart类型映射
```
String → String?          int/long/float/double → num?
boolean → bool?           List<T> → List<T>?
自定义类 → CustomClass?    所有类型都可空（带?）
```

### ✅ 固定规则
- 忽略@Headers注解
- 代码添加在文件末尾
- 自动生成.g.dart文件
- Model路径：`package\{模块名}\lib\model\http\`

---

## 🚀 超快速使用模板

### 基础模板（无model）
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：[模块名]
接口定义：
[完整的Java接口定义代码]
```

### 完整模板（包含model）
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：[模块名]
接口定义：
[完整的Java接口定义代码]

Android Response Model：
[完整的Java Model类代码]
```

## ⚡ 优化后执行流程（30秒完成）
**重要：不要分步骤，不要创建任务列表，直接批量执行！**

1. **一次性解析**：模块类型 + 接口信息 + Model信息
2. **批量生成所有文件**：
   - Model文件（.dart + .g.dart）
   - API常量（api.dart）
   - 接口定义（app_api.dart + 导入）
   - 接口实现（app_api.g.dart）
   - Repository方法（api_repository.dart + 导入）
3. **统一验证**：检查所有文件无错误

**性能提升**：从2-3分钟 → 30秒以内 🚀