import 'package:dio/dio.dart';
import 'package:lib_base/model/http/activity_question_response_model/activity_question_response_model.dart';
import 'package:lib_base/model/http/ad_info.dart';
import 'package:lib_base/model/http/app_exam_paper_model.dart';
import 'package:lib_base/model/http/area_info.dart';
import 'package:lib_base/model/http/book_info.dart';
import 'package:lib_base/model/http/book_info_detail.dart';
import 'package:lib_base/model/http/break_through_level_model.dart';
import 'package:lib_base/model/http/city_high_error_qn_bean.dart';
import 'package:lib_base/model/http/class_detail_info_model.dart';
import 'package:lib_base/model/http/class_info_model.dart';
import 'package:lib_base/model/http/class_member_list_response_model.dart';
import 'package:lib_base/model/http/clazz_dic_info_model.dart';
import 'package:lib_base/model/http/click_learn_ticket_model.dart';
import 'package:lib_base/model/http/common_point_response_model.dart';
import 'package:lib_base/model/http/dict_title_model/dict_title_model.dart';
import 'package:lib_base/model/http/error_word_bean.dart';
import 'package:lib_base/model/http/eyyb_app_version_model.dart';
import 'package:lib_base/model/http/find_word_data_model.dart';
import 'package:lib_base/model/http/learn_course_video_info.dart';
import 'package:lib_base/model/http/listenre_source_column_model.dart';
import 'package:lib_base/model/http/math_dict_list_bean.dart';
import 'package:lib_base/model/http/math_video_bean.dart';
import 'package:lib_base/model/http/new_class_info_model.dart';
import 'package:lib_base/model/http/part_exam_progress_model.dart';
import 'package:lib_base/model/http/practice_report_model.dart';
import 'package:lib_base/model/http/qc_code_audio_info.dart';
import 'package:lib_base/model/http/query_my_resource_model.dart';
import 'package:lib_base/model/http/query_progress_data_model.dart';
import 'package:lib_base/model/http/recommend_model.dart';
import 'package:lib_base/model/http/rjdd_book_relative_model.dart';
import 'package:lib_base/model/http/semester_data_model/semester_data_model.dart';
import 'package:lib_base/model/http/sign_info.dart';
import 'package:lib_base/model/http/statistics_test_submint_item/statistics_test_submint_item.dart';
import 'package:lib_base/model/http/submit_answer_info_model.dart';
import 'package:lib_base/model/http/user_other_info_model.dart';
import 'package:lib_base/model/http/user_with_other_info_for_h5_model.dart';
import 'package:lib_base/model/http/wallet_info.dart';
import 'package:lib_base/model/question_list.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/pages/exam/page/rjdh/model/submit_practice_info_bean.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/dio.dart';
import 'package:lib_base/config/net/base_response.dart';
import 'package:lib_base/config/net/dio.dart';
import 'package:lib_base/config/net/interceptors/http_extra_key.dart';

import '../model/http/dic_item_model.dart';
import '../model/http/collect_word_bean.dart';
import '../model/http/base_page_list_response_model.dart';
import 'api.dart';

part 'app_api.g.dart';

@RestApi()
abstract class BaseAppApi {
  static BaseAppApi? _instance;

  factory BaseAppApi.instance() {
    return BaseAppApi();
  }

  factory BaseAppApi() {
    _instance ??= _BaseAppApi(dio);
    return _instance!;
  }

  @GET(BaseApi.eyyb_app_version)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: true,
    HttpExtraKey.needErrorToast: true,
    HttpExtraKey.hostName: HostName.webHost,
    HttpExtraKey.ignoreResponseLog: true,
  })
  Future<BaseResponse<List<EyybAppVersionModel>>> eyyb_app_version();

  @POST(BaseApi.login)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: true,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<String>> login(
    @Field("loginName") String loginName,
    @Field("loginPwd") String loginPwd,
    @Field("deviceId") String deviceId,
    @Field("phonePlatform") String phonePlatform,
    @Field("appSource") int appSource,
    @Field("appId") String appId,
  );

  @POST(BaseApi.sendMsgCode)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: true,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> sendMsgCode(
      @Field("userPhoneNo") String userPhoneNo,
      @Field("codeType") String codeType,
      @Field("userType") int userType,
      @Field("businessId") String businessId);

  @POST(BaseApi.verifyResetPass)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: true,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> verifyResetPass(
    @Field("username") String username,
    @Field("password") String password,
    @Field("verifyCode") String verifyCode,
    @Field("userType") int userType,
    @Field("appId") String appId,
  );

  @POST(BaseApi.register)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: true,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<String>> register(
    @Field("username") String username,
    @Field("password") String password,
    @Field("deviceId") String deviceId,
    @Field("phonePlatform") String phonePlatform,
    @Field("appSource") String appSource,
    @Field("appId") String appId,
    @Field("verifyCode") String verifyCode,
    @Field("userType") String userType,
  );

  @GET(BaseApi.getPuserInfo)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<PUserInfoEntity>> getPuserInfo();

  @GET(BaseApi.getSignInfo)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<SignInfo>> getSignInfo(
      @Query("userId") String userId, @Query("years") String? years);

  @GET(BaseApi.findWallet)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
    HttpExtraKey.hostName: HostName.payHost,
    HttpExtraKey.hostPort: "",
  })
  Future<BaseResponse<WalletInfo>> findWallet(
    @Query("userId") String userId,
  );

  @GET(BaseApi.queryMyResouce)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<QueryMyResourceModel>> queryMyResouce(
      @Query("types") List<String> types,
      @Query("resourceIds") List<String> resourceIds,
      @Query("moduleIds") List<String> moduleIds,
      @Query("expired") String expired,
      @Query("puserId") String puserId,
      @Query("pageSize") int pageSize,
      @Query("pageIndex") int pageIndex);

  @GET(BaseApi.selectwordtonelist)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
    HttpExtraKey.ignoreResponseLog: true,
  })
  Future<BaseResponse<List<DicItemModel>>> selectwordtonelist();

  @GET(BaseApi.queryAvailableList)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<AdInfo>>> queryAvailableList(
    @Query("positionId") String? positionId,
    @Query("userLevelCode") String? userLevelCode,
    @Query("appInnerVersion") String? appInnerVersion,
  );

  @GET(BaseApi.queryAllRegions)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<AreaInfo>>> queryAllRegions();

  @POST(BaseApi.upuserarea)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> upuserarea(
    @Field("areaCode") String? areaCode,
    @Field("userId") String? userId,
  );

  @GET(BaseApi.getUserWithOtherInfo)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<UserOtherInfoModel>> getUserWithOtherInfo(
    @Query("userId") String? userId,
  );

  @POST(BaseApi.updateUser)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> updateUser(
    @Field("userId") String userId,
    @Field("schoolId") String? schoolId,
    @Field("gradeId") String? gradeId,
    @Field("gradeName") String? gradeName,
    @Field("enVersion") String? enVersion,
    @Field("areaCode") String? areaCode,
    @Field("segment") String? segment,
    @Field("nickName") String? nickName,
    @Field("sex") String? sex,
    @Field("mathVersion") String? mathVersion,
  );

  @GET(BaseApi.queryClazzNum)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<ClazzDicInfoModel>>> queryClazzNum(
    @Query("teacherId") String teacherId,
  );

  @GET(BaseApi.querybook)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
    HttpExtraKey.ignoreResponseLog: true,
  })

  ///version 不传表示查询所有书本(wyxbz 外研社 xsb2 湘少版 xlb 湘鲁版 slb 陕旅版 pep 人教版 )
  ///grade   年级，通用/数据字典接口可以查询到年级列表，不传表示查询所有年级的书本
  Future<BaseResponse<List<BookInfo>>> querybook(
    @Query("version") String? version,
    @Query("grade") String? grade,
    @Query("objective") String? objective,
    @Query("subject") String subject,
  );

  @GET(BaseApi.checknewvippe)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> checknewvippe(@Query("userId") String userId,
      @Query("areaCode") String areaCode, @Query("schoolId") String schoolId);

  @GET(BaseApi.getJoinedClazzList)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<ClassInfoModel>>> getJoinedClazzList(
    @Query("studentId") String studentId,
  );

  @POST(BaseApi.updatePriority)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> updatePriority(
    @Field("clazzId") String clazzId,
  );

  @GET(BaseApi.getClazzInfo)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ClassDetailInfoModel>> getClazzInfo(
    @Query("clazzId") String clazzId,
  );

  @GET(BaseApi.queryclazzbyclazznonew)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<NewClassInfoModel>> queryclazzbyclazznonew(
    @Query("clazzNo") String clazzNo,
  );

  @POST(BaseApi.checksydata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> checksydata(
    @Field("contentList") List<String> contentList,
    @Field("test") String? test,
  );

  @POST(BaseApi.exitClazz)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> exitClazz(
    @Field("clazzId") String clazzId,
    @Field("studentId") String? studentId,
  );

  @GET(BaseApi.queryMemberByStudent)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<ClassMemberListResponseModel>> queryMemberByStudent(
      @Query("clazzId") String clazzId,
      @Query("userId") String userId,
      @Query("pageIndex") int pageIndex,
      @Query("pageSize") int pageSize,
      @Query("orderBy") String? orderBy);

  @POST(BaseApi.dynamicUplike)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse> dynamicUplike(
    @Field("userId") String userId,
    @Field("dynamicId") String dynamicId,
  );

  @GET(BaseApi.queryBookRelationMap)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<List<RjddBookRelativeModel>>> queryBookRelationMap();

  @GET(BaseApi.getbook)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<BookInfoDetail>> getbook(
    @Query("bookId") String? bookId,
    @Query("versionNum") String? versionNum,
    @Query("appId") String? appId,
    @Query("userId") String? userId,
  );

  @GET(BaseApi.onebookdata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<BookInfoDetail>> onebookdata(
    @Query("id") String? id,
    @Query("versionNum") String? versionNum,
    @Query("appId") String? appId,
    @Query("userId") String? userId,
  );

  @GET(BaseApi.listenresourcecolumn)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<ListenreSourceColumnModel>>> listenresourcecolumn(
    @Query("grade") String? grade,
  );

  @POST(BaseApi.updatePoint)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<String>> updatePoint(
    @Field("ruleCode") String? ruleCode,
    @Field("moduleId") String? moduleId,
    @Field("detailId") String? detailId,
  );

  @GET(BaseApi.queryrecommendList)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<RecommendModel>>> queryrecommendList(
      @Query("subject") String subject,
      @Query("recommendPoint") String recommendPoint,
      @Query("bookId") String? bookId,
      @Query("grade") String? grade,
      @Query("versionNum") String versionNum);

  @POST(BaseApi.readresultnew)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> readresultnew(
    @Field("oralunitId") String oralunitId,
    @Field("answers") String answers,
  );

  @GET(BaseApi.queryappexampaper)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<AppExamPaperModel>> queryappexampaper(
    @Query("id") String id,
  );

  @POST(BaseApi.submitexam)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> submitexam(
    @Field("provinceCity") String? provinceCity,
    @Field("grade") String? grade,
    @Field("userId") String userId,
    @Field("moduleId") String moduleId,
    @Field("examPaperId") String? examPaperId,
    @Field("examName") String? examName,
    @Field("examTypeName") String? examTypeName,
    @Field("jointExamineName") String? jointExamineName,
    @Field("tf") String? tf,
    @Field("trueCount") int? trueCount,
    @Field("falseCount") int? falseCount,
    @Field("halfCount") int? halfCount,
    @Field("tfRate") num? tfRate,
    @Field("results") List<String> results,
    @Field("rightResults") List<String> rightResults,
    @Field("costSecond") int? costSecond,
    @Field("everyCostSecond") String? everyCostSecond,
    @Field("questionIds") List<String> questionIds,
    @Field("images") List<String>? images,
    @Field("vedios") List<String>? vedios,
    @Field("audios") List<String>? audios,
    @Field("dedicatedRegion") String? dedicatedRegion,
    @Field("totalScore") num? totalScore,
    @Field("score") num? score,
    @Field("completionRate") num? completionRate,
    @Field("examCard") String? examCard,
    @Field("id") String id,
    @Field("subject") String subject,
  );

  @POST(BaseApi.saveuserunitexampapercompletionprog)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> saveuserunitexampapercompletionprog(
    @Field("bookId") String bookId,
    @Field("bookUnitId") String bookUnitId,
    @Field("paramId") String? paramId,
    @Field("subId") String subId,
    @Field("resourceId") String resourceId,
    @Field("userId") String userId,
    @Field("studyTime") int studyTime,
    @Field("qusNum") int qusNum,
    @Field("remark") String remark,
  );

  @POST(BaseApi.saveuserkwpycompletionprog)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> saveuserkwpycompletionprog(
    @Field("bookId") String bookId,
    @Field("paramId") String? paramId,
    @Field("subId") String subId,
    @Field("userId") String userId,
    @Field("studyTime") int studyTime,
    @Field("remark") String remark,
  );

  @GET(BaseApi.queryprogressdata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<QueryProgressDataModel>> queryprogressdata(
    @Query("id") String? id,
    @Query("type") String? type,
    @Query("subject") String? subject,
  );

  @GET(BaseApi.querypartexamprogress)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<PartExamProgressModel>> querypartexamprogress(
    @Query("id") String? id,
    @Query("subject") String? subject,
  );

  @GET(BaseApi.querytodayerrorqn)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<QuestionList>>> querytodayerrorqn(
    @Query("userId") String? userId,
    @Query("subject") String? subject,
  );

  @GET(BaseApi.querynorevisionerrorqn)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<QuestionList>>> querynorevisionerrorqn(
    @Query("userId") String? userId,
    @Query("subject") String? subject,
    @Query("type") String? type,
    @Query("bookId") String? bookId,
    @Query("unitId") String? unitId,
    @Query("qyType") String? qyType,
    @Query("labelId") String? labelId,
  );

  // JSONObject json = new JSONObject();
  // json.put("provinceCity", provinceCity);
  // json.put("grade", grade);
  // json.put("userId", GloableParams.getCurrUserId());
  // json.put("subject", subject);
  // json.put("results", results);
  // json.put("tf", tf);
  // json.put("questionIds", questionIds);
  // json.put("everyCostSecond", everyCostSecond);
  // json.put("resourceIds", resourceIds);
  @POST(BaseApi.submiterrorquestions)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> submiterrorquestions(
    @Field("provinceCity") String? provinceCity,
    @Field("grade") String? grade,
    @Field("userId") String? userId,
    @Field("subject") String? subject,
    @Field("results") List<String>? results,
    @Field("tf") String? tf,
    @Field("questionIds") List<String>? questionIds,
    @Field("everyCostSecond") String? everyCostSecond,
    @Field("resourceIds") List<String>? resourceIds,
  );

  @GET(BaseApi.querybreakthroughlevel)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<BreakThroughLevelModel>>> querybreakthroughlevel(
    @Query("id") String? id,
    @Query("userId") String? userId,
    @Query("moduleId") String? moduleId,
  );

  @GET(BaseApi.queryapplevelquestion)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<QuestionList>>> queryapplevelquestion(
    @Query("id") String? id,
    @Query("userId") String? userId,
    @Query("questionNum") String? questionNum,
  );

  @POST(BaseApi.submitpractice)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> submitpractice(
    @Field("provinceCity") String? provinceCity,
    @Field("grade") String? grade,
    @Field("userId") String userId,
    @Field("moduleId") String moduleId,
    @Field("breakthroughUnitId") String? breakthroughUnitId,
    @Field("breakthroughLevelId") String? breakthroughLevelId,
    @Field("exerciseUnitName") String? exerciseUnitName,
    @Field("gearName") String? gearName,
    @Field("levelName") String? levelName,
    @Field("trueCount") int? trueCount,
    @Field("falseCount") int? falseCount,
    @Field("halfCount") int? halfCount,
    @Field("tfRate") num? tfRate,
    @Field("results") List<String> results,
    @Field("rightResults") List<String> rightResults,
    @Field("costSecond") int? costSecond,
    @Field("everyCostSecond") String? everyCostSecond,
    @Field("questionIds") List<String> questionIds,
    @Field("images") List<String>? images,
    @Field("vedios") List<String>? vedios,
    @Field("audios") List<String>? audios,
    @Field("tf") String? tf,
    @Field("subject") String subject,
  );

  @GET(BaseApi.findworddata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<FindWordDataModel>> findworddata(
    @Query("wordName") String? wordName,
    @Query("unitId") String? unitId,
  );

  @GET(BaseApi.queryClickLearnTicket)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<ClickLearnTicketModel>>> queryClickLearnTicket(
    @Query("userId") String? userId,
    @Query("bookId") String? bookId,
    @Query("useFlag") String? useFlag,
    @Query("overdueFlag") String? overdueFlag,
  );

  @GET(BaseApi.getUserWithOtherInfoForH5)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<UserWithOtherInfoForH5Model>> getUserWithOtherInfoForH5(
    @Query("userId") String? userId,
  );

  @GET(BaseApi.verifyManagePassword)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> verifyManagePassword(
    @Query("userId") String? userId,
    @Query("password") String? password,
    @Query("enpassword") String? enpassword,
  );

  @POST(BaseApi.changeManagePass)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> changeManagePass(
    @Field("userId") String? userId,
    @Field("password") String? password,
    @Field("oldPass") String? oldPass,
  );

  @POST(BaseApi.verifyResetManagePass)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> verifyResetManagePass(
    @Field("userId") String? userId,
    @Field("password") String? password,
    @Field("phone") String? phone,
    @Field("verifyCode") String? verifyCode,
  );

  @POST(BaseApi.redeemClickLearnTicket)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> redeemClickLearnTicket(
    @Field("userId") String? userId,
    @Field("bookId") String? bookId,
    @Field("id") String? id,
    @Field("phonePlatform") String? phonePlatform,
  );

  @GET(BaseApi.querysemesterdata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<SemesterDataModel>> querysemesterdata(
    @Query("userId") String? userId,
    @Query("subject") String? subject,
  );

  @POST(BaseApi.usehead)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> usehead(
    @Field("appId") String? appId,
    @Field("headId") String? headId,
    @Field("glory") String? glory,
  );

  //   @POST("/behavior-api/api/yyb/v1/startthe/save")
  // params.put("userId", GloableParams.getCurrUserId());
  //       params.put("appVersion", Utils.getVersion(BaseApp.getInstance()));
  //       params.put("phoneName", Build.BRAND);
  //       params.put("phoneModel", Build.MODEL);
  //       params.put("platformName", "Android");
  //       params.put("platformVersion", Build.VERSION.RELEASE);
  //       params.put("osName", Build.VERSION.SDK_INT);
  //       params.put("deviceId", Utils.getDeviceId(getApplicationContext()));
  //       params.put("userType", currUser.getType());
  //       params.put("appId", GloableParams.getAppId());
  //       params.put("address", GloableParams.getUserAddress());
  //       //App启动来源(短信、公众号)
  //       try {
  //           params.put("source", getIntent().getStringExtra("startSource"));
  //       } catch (Throwable throwable) {
  //           params.put("source", "error");
  //       }
  @POST(BaseApi.savestartthe)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse> savestartthe(
    @Field("userId") String? userId,
    @Field("appVersion") String? appVersion,
    @Field("phoneName") String? phoneName,
    @Field("phoneModel") String? phoneModel,
    @Field("platformName") String? platformName,
    @Field("platformVersion") String? platformVersion,
    @Field("osName") String? osName,
    @Field("deviceId") String? deviceId,
    @Field("userType") String? userType,
    @Field("appId") String? appId,
    @Field("address") String? address,
    @Field("source") String? source,
  );

  // @GET("/appconfig-api/api/yyb/v1/dict/phone/getDictTitle")
  // Call<JsonObject> getDictTitle(@Query("types") String type);
  @GET(BaseApi.getDictTitle)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<DictTitleModel>>> getDictTitle(
    @Query("types") String type,
  );

  //  @POST("/api/homework/v50/student/updateprodata")
  // params.put("homeworkId", zzySubmitItem.getHomeworkId());
  //           params.put("images", zzySubmitItem.getImages());
  //           params.put("vedios", zzySubmitItem.getVedios());
  //           params.put("audios", zzySubmitItem.getAudios());
  //           params.put("results", zzySubmitItem.getResults());
  //           params.put("moduleId", zzySubmitItem.getModuleId());
  //           if (!TextUtils.isEmpty(GloableParams.getCurrentClazzId())){
  //               params.put("clazzId", GloableParams.getCurrentClazzId());
  //           }else {
  //               params.put("clazzId", zzySubmitItem.getClazzId());
  //           }
  //           params.put("id",id);

  @POST(BaseApi.updateprodata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> updateprodata(
    @Field("homeworkId") String? homeworkId,
    @Field("images") List<String>? images,
    @Field("vedios") List<String>? vedios,
    @Field("audios") List<String>? audios,
    @Field("results") List<String>? results,
    @Field("moduleId") String? moduleId,
    @Field("clazzId") String? clazzId,
    @Field("id") String? id,
  );

  @POST(BaseApi.submithk)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> submithk(
    @Field("provinceCity") String? provinceCity,
    @Field("grade") String? grade,
    @Field("userId") String? userId,
    @Field("type") String? type,
    @Field("detailId") String? detailId,
    @Field("homeworkId") String? homeworkId,
    @Field("tf") String? tf,
    @Field("everyCostSecond") String? everyCostSecond,
    @Field("images") List<String>? images,
    @Field("costSecond") String? costSecond,
    @Field("vedios") List<String>? vedios,
    @Field("audios") List<String>? audios,
    @Field("results") List<String>? results,
    @Field("rightResults") List<String>? rightResults,
    @Field("moduleId") String? moduleId,
    @Field("tfRate") String? tfRate,
    @Field("receiveVal") String? receiveVal,
    @Field("academicYear") String? academicYear,
    @Field("fascicule") String? fascicule,
    @Field("noQns") List<String>? noQns,
    @Field("clazzId") String? clazzId,
  );

  // @POST("/api/textbook/v50/syncquestion/updateappstatus")
  //   Call<JSONObject> updateappstatus(@Body RequestBody body);
  //  JSONObject params = new JSONObject();
  //       params.put("phonePlatform", "Android");
  //       params.put("auditStatus", auditStatus);
  //       if (!TextUtils.isEmpty(reason)) {
  //           params.put("reason", reason);
  //       }
  //       params.put("id", id);
  //       params.put("userId", GloableParams.getCurrUserId());
  @POST(BaseApi.updateappstatus)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> updateappstatus(
    @Field("auditStatus") String? auditStatus,
    @Field("reason") String? reason,
    @Field("id") String? id,
    @Field("userId") String? userId,
  );

  // @POST("/api/questionrecord/v50/hkrevision/updatetfresults")
  //  JSONObject json = new JSONObject();
  //       json.put("dateName", dateName);
  //       json.put("ids", ids);
  //       json.put("tfResults", tfResults);
  //       json.put("clazzId", clazzId);
  //       json.put("userId", GloableParams.getCurrUserId());
  //       json.put("homeworkId", homeworkId);
  //       json.put("homeworkType", homeworkType);
  //       json.put("questionIds", questionIds);
  @POST(BaseApi.updatetfresults)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> updatetfresults(
    @Field("dateName") String? dateName,
    @Field("ids") List<String>? ids,
    @Field("tfResults") List<String>? tfResults,
    @Field("clazzId") String? clazzId,
    @Field("userId") String? userId,
    @Field("homeworkId") String? homeworkId,
    @Field("homeworkType") String? homeworkType,
    @Field("questionIds") List<String>? questionIds,
  );

  @GET(BaseApi.queryAllModules)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> queryAllModules(@Query("userId") String userId);

  /**
     * 提交统测活动对应测试卷
     *
     * @return
     */
  // @POST("/activity-api/api/activity/v50/unifiedtestsum/submitunifiedtestexam")
  // JSONObject params = new JSONObject();
  //     params.put("provinceCity", GloableParams.getCurrUser().getAreaCode());
  //     params.put("grade", TextUtils.isEmpty(GloableParams.getCurrUser().getGradeId()) ? "threeGrade" : GloableParams.getCurrUser().getGradeId());
  //     params.put("id", submitItem.getId());
  //     params.put("examName", submitItem.getExamName());
  //     params.put("examPaperId", submitItem.getExamPaperId());
  //     params.put("results", submitItem.getResults());
  //     params.put("userId", GloableParams.getCurrUserId());
  //     params.put("falseCount", submitItem.getFalseCount());
  //     params.put("trueCount", submitItem.getTrueCount());
  //     params.put("costSecond", submitItem.getCostSecond());
  //     params.put("everyCostSecond", submitItem.getEveryCostSecond());
  //     params.put("questionIds", submitItem.getQuestionIds());
  //     params.put("questionScore", submitItem.getQuestionScore());
  //     params.put("answerScore", submitItem.getAnswerScores());
  //     params.put("tfRate", submitItem.getTfRate());
  //     params.put("rightResults", submitItem.getRightResults());
  //     params.put("resourcesId", submitItem.getResourcesId());
  //     params.put("clazzId", submitItem.getClazzId());
  //     params.put("halfCount", submitItem.getHalfCount());
  //     params.put("seriesName", submitItem.getSeriesName());
  //     params.put("seriesId", submitItem.getSeriesId());
  //     params.put("tf", submitItem.getTf());
  //     params.put("areaCode", submitItem.getAreaCode());
  //     params.put("schoolId", submitItem.getSchoolId());
  //     params.put("examCard", submitItem.getExamCard());
  //     params.put("totalScore", submitItem.getTotalScore());
  //     params.put("score", submitItem.getScore());
  //     params.put("completionRate", submitItem.getCompletionRate());
  //     params.put("examDuration", submitItem.getExamDuration());
  @POST(BaseApi.submitunifiedtestexam)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> submitunifiedtestexam(
    @Field("provinceCity") String? provinceCity,
    @Field("grade") String? grade,
    @Field("id") String? id,
    @Field("examName") String? examName,
    @Field("examPaperId") String? examPaperId,
    @Field("results") List<String>? results,
    @Field("userId") String? userId,
    @Field("falseCount") int? falseCount,
    @Field("trueCount") int? trueCount,
    @Field("costSecond") int? costSecond,
    @Field("everyCostSecond") String? everyCostSecond,
    @Field("questionIds") List<String>? questionIds,
    @Field("questionScore") List<String>? questionScore,
    @Field("answerScore") List<String>? answerScore,
    @Field("tfRate") int? tfRate,
    @Field("rightResults") List<String>? rightResults,
    @Field("resourcesId") String? resourcesId,
    @Field("clazzId") String? clazzId,
    @Field("halfCount") int? halfCount,
    @Field("seriesName") String? seriesName,
    @Field("seriesId") String? seriesId,
    @Field("tf") String? tf,
    @Field("areaCode") String? areaCode,
    @Field("schoolId") String? schoolId,
    @Field("examCard") String? examCard,
    @Field("totalScore") int? totalScore,
    @Field("score") double? score,
    @Field("completionRate") int? completionRate,
    @Field("examDuration") int? examDuration,
  );

  /**
     * 查询部分完成详情
     *
     * @return
     */
  // @GET("/activity-api/api/activity/v50/unifiedtestsum/querypartunifiedtestprogress")
  // Call<JSONObject> querypartunifiedtestprogress(@Query("id") String id);
  @GET(BaseApi.querypartunifiedtestprogress)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<StatisticsTestSubmintItem>> querypartunifiedtestprogress(
    @Query("id") String? id,
  );

  /**
     * 获取题目列表
     * @param activityId 活动id
     * @param ids 0、邀请好友获得参赛次数、1、邀请好友获得积分、2、分享获得参赛次数
     * @param wordIds
     * @return
     */
  // @GET("/activity-api/api/yyb/v1/activity/api/queryQuestions")
  // Call<JSONObject> queryQuestions(@Query("activityId") String activityId,
  //                                 @Query("ids") String ids,
  //                                 @Query("wordIds") String wordIds,
  //                                 @Query("versionId") String versionId,
  // @Query("userId") String userId);
  @GET(BaseApi.queryQuestions)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ActivityQuestionResponseModel>> queryQuestions(
    @Query("activityId") String? activityId,
    @Query("ids") String? ids,
    @Query("wordIds") String? wordIds,
    @Query("versionId") String? versionId,
    @Query("userId") String? userId,
  );

  // @POST("/activity-api/api/yyb/v1/activity/api/submitAnsersInfo")
  // JSONObject jsonObject = new JSONObject();
  //     jsonObject.put("activityId", activityId);
  //     jsonObject.put("answers", answers);
  //     jsonObject.put("totalQuesCount", totalQuesCount);
  //     jsonObject.put("successQuesCount", successQuesCount);
  //     jsonObject.put("rightRate", rightRate);
  //     jsonObject.put("spendSeconds", spendSeconds);
  //     jsonObject.put("startTime", startTime);
  //     jsonObject.put("endTime", endTime);
  //     jsonObject.put("faildQuesCount", faildQuesCount);
  @POST(BaseApi.submitAnsersInfo)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<SubmitAnswerInfoModel>> submitAnsersInfo(
    @Field("activityId") String? activityId,
    @Field("answers") String? answers,
    @Field("totalQuesCount") String? totalQuesCount,
    @Field("successQuesCount") String? successQuesCount,
    @Field("rightRate") String? rightRate,
    @Field("spendSeconds") String? spendSeconds,
    @Field("startTime") String? startTime,
    @Field("endTime") String? endTime,
    @Field("faildQuesCount") String? faildQuesCount,
  );

  /**
     * 答题中错误的单词提交到措词本中（ui接口）
     * @return
     */
  // @POST("/word-api/api/yyb/v1/qiaoxue/phone/saveErrorWord")
  // JSONObject jsonObject = new JSONObject();
  //     jsonObject.put("errorWords", lists);
  @POST(BaseApi.saveErrorWord)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> saveErrorWord(
    @Field("errorWords") List<ErrorWordBean>? errorWords,
    @Field("test") String? test,
  );

  /**
     * 提交不需要上传资源的公共模块题提交接口
     *
     * @param body
     * @return
     */
  // @POST("/api/questionrecord/v50/errorqn/addpubquestionpro")
  //  com.alibaba.fastjson.JSONObject params = new com.alibaba.fastjson.JSONObject();
  //     params.put("provinceCity", GloableParams.getCurrUser().getAreaCode());
  //     params.put("grade", TextUtils.isEmpty(GloableParams.getCurrUser().getGradeId()) ? "threeGrade" : GloableParams.getCurrUser().getGradeId());
  //     params.put("userId", GloableParams.getCurrUserId());
  //     params.put("subject","english");
  //     params.put("results",mistakesSubmitBean.getResults());
  //     params.put("tf",mistakesSubmitBean.getTf());
  //     params.put("questionIds",mistakesSubmitBean.getQuestionIds());
  //     params.put("everyCostSecond",mistakesSubmitBean.getEveryCostSecond());
  //     params.put("moduleId",mistakesSubmitBean.getModuleId());
  //     params.put("sourceType",mistakesSubmitBean.getSourceType());
  //     params.put("specificType",mistakesSubmitBean.getSpecificType());
  @POST(BaseApi.addpubquestionpro)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> addpubquestionpro(
    @Field("provinceCity") String? provinceCity,
    @Field("grade") String? grade,
    @Field("userId") String? userId,
    @Field("subject") String? subject,
    @Field("results") List<String>? results,
    @Field("tf") String? tf,
    @Field("questionIds") List<String>? questionIds,
    @Field("everyCostSecond") String? everyCostSecond,
    @Field("moduleId") String? moduleId,
    @Field("sourceType") String? sourceType,
    @Field("specificType") String? specificType,
  );

  @GET(BaseApi.getMyBestResult)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> getMyBestResult(
      @Query("userId") String? userId, @Query("activityId") String? activityId);

  @GET(BaseApi.domain)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
    HttpExtraKey.hostName: HostName.domainhost
  })
  Future<String> domain();

  /**
     * 600查询部分答完的口语模测详情数据
     *
     * @return
     */
  // @GET("/api/textbook/v50/breakthrough/querypartoralmtestpro")
  // Call<JSONObject> querypartoralmtestpro(@Query("id") String id, @Query("subject") String subject);
  @GET(BaseApi.querypartoralmtestpro)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<StatisticsTestSubmintItem>> querypartoralmtestpro(
    @Query("id") String? id,
    @Query("subject") String? subject,
  );

  /**
     * 600提交考前口语模测
     *
     * @return
     */
  // @POST("/api/studyrecord/v50/breakthrough/submitoralmtest")
  @POST(BaseApi.submitoralmtest)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> submitoralmtest(
    @Field("userId") String? userId,
    @Field("moduleId") String? moduleId,
    @Field("examPaperId") String? examPaperId,
    @Field("examName") String? examName,
    @Field("examTypeName") String? examTypeName,
    @Field("jointExamineName") String? jointExamineName,
    @Field("tf") String? tf,
    @Field("falseCount") int? falseCount,
    @Field("trueCount") int? trueCount,
    @Field("halfCount") int? halfCount,
    @Field("tfRate") num? tfRate,
    @Field("results") List<String>? results,
    @Field("rightResults") List<String>? rightResults,
    @Field("costSecond") int? costSecond,
    @Field("everyCostSecond") String? everyCostSecond,
    @Field("questionIds") List<String>? questionIds,
    @Field("dedicatedRegion") String? dedicatedRegion,
    @Field("totalScore") num? totalScore,
    @Field("score") num? score,
    @Field("completionRate") num? completionRate,
    @Field("examCard") String? examCard,
    @Field("id") String? id,
    @Field("subject") String? subject,
    @Field("questionScore") List<String>? questionScore,
    @Field("examDuration") int? examDuration,
    @Field("answerScore") List<String>? answerScore,
  );

  /**
     * 查询生词本id
     *
     * @return
     */
  // @GET("/api/textbook/v50/independent/findindependenttype")
  // Call<JSONObject> findindependenttype(@Query("type") String type);
  @GET(BaseApi.findindependenttype)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<String>> findindependenttype(
    @Query("type") String? type,
  );

  // @GET("/api/textbook/v50/scan")
  // Call<JSONObject> queryQRCodeAudio(@Query("type") String type, @Query("id") String id);
  @GET(BaseApi.queryQRCodeAudio)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<QcCodeAudioInfo>> queryQRCodeAudio(
    @Query("type") String? type,
    @Query("id") String? id,
  );

  // @GET("/api/textbook/v50/manmachinetalkapi/selectquestionbypractice")
  // Call<JSONObject> selectquestionbypractice(@Query("practiceId") String practiceId);
  @GET(BaseApi.selectquestionbypractice)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<QuestionList>>> selectquestionbypractice(
    @Query("practiceId") String? practiceId,
  );

  @GET(BaseApi.selectpracticereport)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<PracticeReportModel>> selectpracticereport(
    @Query("breakthroughId") String? breakthroughId,
    @Query("unitId") String? unitId,
    @Query("userId") String? userId,
    @Query("practiceId") String? practiceId,
  );

// com.alibaba.fastjson.JSONObject json = new com.alibaba.fastjson.JSONObject();
//         json.put("breakthroughId", breakthroughId);
//         json.put("breakthroughExerciseUnitId", breakthroughExerciseUnitId);
//         json.put("practiceId", practiceId);
//         json.put("recordList", infoBeanList);
//         json.put("completeRate", completeRate);
//         RequestBody body = RequestBody.create(okhttp3.MediaType.parse("application/json; charset=UTF-8"), json.toString());
  @POST(BaseApi.submitpracticeinfo)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ResultInfoBean>> submitpracticeinfo(
    @Field("breakthroughId") String? breakthroughId,
    @Field("breakthroughExerciseUnitId") String? breakthroughExerciseUnitId,
    @Field("practiceId") String? practiceId,
    @Field("recordList") List<SubmitPracticeInfoBean>? recordList,
    @Field("completeRate") num? completeRate,
  );

  // @GET("/api/word/v50/train/camp/course/videooptions")
  // Call<JsonObject> queryMathLearnCourseVideoInfo(@Query("gmManageId") String gmManageId);
  @GET(BaseApi.videooptions)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<LearnCourseVideoInfo>> videooptions(
    @Query("gmManageId") String? gmManageId,
  );

  // @POST("/api/studyrecord/v50/train/camp/course/submitwatch")
  // Call<JSONObject> submitMathLearnCourseVideoWatchRecord(@Body RequestBody body);
  // //训练营Id
  //       json.put("trainingCampId", trainingCampId);
  //       //营期Id
  //       json.put("tcManageId", tcManageId);
  //       //主题Id
  //       json.put("manageId", manageId);
  //       //用户Id
  //       json.put("userId", userId);
  //       //学习进度(整数百分数)
  //       json.put("watchSpeed", watchSpeed);
  //       //学习时长(单位:秒)
  //       json.put("watchTime", watchTime);
  @POST(BaseApi.submitwatch)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<CommonPointResponseModel>> submitwatch(
    @Field("trainingCampId") String? trainingCampId,
    @Field("tcManageId") String? tcManageId,
    @Field("manageId") String? manageId,
    @Field("userId") String? userId,
    @Field("watchSpeed") int? watchSpeed,
    @Field("watchTime") int? watchTime,
  );

  @POST(BaseApi.addSentenceRecord)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
    HttpExtraKey.hostName: HostName.loghost,
  })
  Future<BaseResponse> addSentenceRecord(
    @Field("contentId") String? contentId,
    @Field("content") String? content,
    @Field("type") String? type,
    @Field("bookId") String? bookId,
    @Field("remarks") String? remarks,
    @Field("referenceId") String? referenceId,
    @Field("requestId") String? requestId,
  );

  @POST(BaseApi.sign)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<int>> sign(
    @Field("userId") String userId,
  );

  /**
   * 根据一级单元id查询视频资源
   */
  @GET(BaseApi.selectvideoresourcelist)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<MathVideoBean>> selectvideoresourcelist(
    @Query("bookId") String bookId,
    @Query("oneUnitId") String oneUnitId,
    @Query("userId") String userId,
  );

  /**
   * 查询书本对应一级单元
   */
  @GET(BaseApi.selectunitlistbybook)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<MathDictListBean>>> selectunitlistbybook(
    @Query("bookId") String? bookId,
  );

  /**
   * 查询市高频错题列表
   */
  @GET(BaseApi.querycityhigherrorqn)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<CityHighErrorQnBean>> querycityhigherrorqn(
    @Query("resourceId") String? resourceId,
    @Query("userId") String? userId,
  );

  /**
   * 保存地区高频错题提交结果
   */
  @POST(BaseApi.submithighqnpro)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<dynamic>> submithighqnpro(
    @Field("provinceCity") String? provinceCity,
    @Field("grade") String? grade,
    @Field("userId") String? userId,
    @Field("subject") String? subject,
    @Field("resourceId") String? resourceId,
    @Field("tfRate") String? tfRate,
    @Field("results") List<String>? results,
    @Field("rightResults") List<String>? rightResults,
    @Field("tf") String? tf,
    @Field("costSecond") num? costSecond,
    @Field("questionIds") List<String>? questionIds,
    @Field("everyCostSecond") String? everyCostSecond,
  );

  @POST(BaseApi.submitweekqnpro)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> submitweekqnpro(
    @Field("provinceCity") String? provinceCity,
    @Field("grade") String? grade,
    @Field("userId") String? userId,
    @Field("subject") String? subject,
    @Field("resourceId") String? resourceId,
    @Field("tfRate") String? tfRate,
    @Field("results") List<String>? results,
    @Field("rightResults") List<String>? rightResults,
    @Field("tf") String? tf,
    @Field("costSecond") num? costSecond,
    @Field("questionIds") List<String>? questionIds,
    @Field("everyCostSecond") String? everyCostSecond,
  );

  @POST(BaseApi.queryrevisionerrorqn)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<QuestionList>>> queryrevisionerrorqn(
    @Field("subject") String? subject,
    @Field("userId") String? userId,
    @Field("strList") List<String>? strList,
    @Field("questionIds") List<String>? questionIds,
  );



  @GET(BaseApi.queryweekqn)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<CityHighErrorQnBean>> queryweekqn(
    @Query("userId") String? userId,
    @Query("id") String? id,
  );

  /**
   * 用户查询单词收藏
   */
  @GET(BaseApi.findWordListByUserId)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<BasePageListResponseModel<CollectWordBean>>> findWordListByUserId(
    @Query("pageNo") int pageIndex,
    @Query("pageSize") int pageSize,
  );


}
