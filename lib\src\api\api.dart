class Api {
  ///查询配置
  static const String findByAppId =
      "/appconfig-api/api/yyb/v1/set/phone/findByAppId";

  /// 批量查询广告
  static const String listavailables =
      "/advertise-api/api/yyb/v1/advertise/info/api/listavailables";

  ///查询
  static const String queryadvertise =
      "/advertise-api/api/v50/advertise/ai/queryadvertise";

  ///查询英语课程
  static const String queryEnglishCourse =
      "/api/yyb/v1/period/phone/queryEnglishCourse";

  ///查询英语兴趣
  static const String queryenglishinterestthree =
      "/api/interest/v50/dubbing/queryenglishinterestthree";

  ///查询直播 liveType 直播类型(0:教研直播;1:教学直播;2:学生直播;多个为逗号拼接)
  ///这个接口已经404了
  static const String findlivebytype =
      "/live-api/api/yyb/live/api/liveinfocontroller/findlivebytype";

  /// /**
  //      * 5.8.0版本短视频-分页查询短视频列表
  //      *
  //      * @param userId   用户id
  //      * @param qryType  查询类型(1:最多点赞最多浏览最多分享排序的;2:查询最新的;3:最多点赞的;4:最多点赞里最新发布的)
  //      * @param pageNo   第几页
  //      * @param pageSize 一页多少条
  //      * @param name     视频名称
  //      * @return
  //      */
  static const String pageappshortvideodata =
      "/api/course/v50/shortvideo/pageappshortvideodata";

  static const String getRecommendActivity =
      "/activity-api/api/yyb/v1/activity/api/getRecommendActivity";

  //短视频分类
  static const String selectshortvideoallseries =
      "/api/course/v50/shortvideo/selectshortvideoallseries";

  //780   语文、数学改版接口
  static const String querychineseexpand =
      "/api/yyb/v1/period/phone/querychineseexpand";

  // 历史最佳成绩接口
  static const String getMyBestResult =
      "/activity-api/api/yyb/v1/activity/api/getMyBestResult";

  // 历史最佳成绩接口
  static const String queryAreaSchool =
      "/school-api/api/yyb/v1/schoolApi/queryAreaSchool";

  /**
   * 查询教材信息 -- V7.8.1 20240810
   * (个人信息选择教材版本、用户注册选择教材版本)
   *
   * @param subject   科目
   * @param version   版本
   * @param grade     年级
   * @param fascicule 上下册
   * @return
   */
  static const String listbkversion = "/api/textbook/v50/book/listbkversion";

  /**
   * 查询教材信息 -- V7.8.1 20240810
   * (个人信息选择教材版本、用户注册选择教材版本)
   *
   * @param subject   科目
   * @param version   版本
   * @param grade     年级
   * @param fascicule 上下册
   * @return
   */
  static const String querybooknominatebyarea =
      "/api/school/v50/areabook/querybooknominatebyarea";

  /**
   * 查询APP文案
   *
   * @return
   */
  static const String explainPhone = "/appconfig-api/api/yyb/v1/explain/phone";

  /**
   * 添加反馈
   *
   * @return
   */
  static const String addadvice = "/api/comments/v50/advice/addadvice";

  /**
   * 反馈列表
   *
   * @return
   */
  static const String finduseradvices =
      "/api/comments/v50/advice/finduseradvices";

  /**
   * 修改密码
   *
   * @return
   */
  static const String changePass = "/api/yyb/v1/user/api/changePass";

  /**
   * 验证新手机
   *
   * @return
   */
  static const String verifyNewPhone = "/api/yyb/v1/user/api/verifyNewPhone";

  /**
   * 验证旧手机
   *
   * @return
   */
  static const String verifyOldPhone = "/api/yyb/v1/user/api/verifyOldPhone";

  /**
   * 注销账号
   *
   * @return
   */
  static const String close = "/api/yyb/v1/login/api/close";

  /**
   * V7.8.1发现页改版-查询发现页信息
   *
   * @param userId 用户Id
   * @param grade  年级(默认为三年级)
   * @return
   */
  static const String discovereddata =
      "/word-api/api/word/v50/campd/discovereddata";

  /**
   * V7.8.1发现页改版-查询训练营信息
   *
   * @param grade 年级(默认为三年级)
   * @return
   */
  static const String listcampdata =
      "/word-api/api/word/v50/campd/listcampdata";

  //用户是否可以看到发现页面的AI口语模块
  static const String checkuserblend =
      '/api/textbook/v50/aispeak/checkuserblend';

  /**
   * 学生申请加入班级带学号新接口
   *
   * @param body
   * @return
   */
  static const String applyjoinclazzstudentnumber =
      "/user-api/api/yyb/v1/clazzStudent/applyjoinclazzstudentnumber";

  /**
   * 已兑头像
   */
  static const String listmyhead = "/api/user/v50/user/api/listmyhead";

  /**
   * 头像管理
   */
  static const String getheadinfo = "/api/user/v50/user/api/getheadinfo";

  /**
   * 使用头像
   */
  static const String usehead = "/api/user/v50/user/api/usehead";

  /**
   * 兑换头像
   */
  static const String redemptionhead = "/api/user/v50/user/api/redemptionhead";

  /**
   * 荣耀头像
   */
  static const String gloryList = "/api/user/v50/glory/list";

  /**
   * V7.8.1发现页改版-查询发现页购买信息
   *
   * @param userId 用户Id
   * @return
   */
  static const String listordercampandju =
      "/word-api/api/word/v50/campd/listordercampandju";

  /**
   * 修改班级学生学号
   *
   * @return
   */
  static const String upclazzstudentnum =
      "/user-api/api/yyb/v1/clazzStudent/upclazzstudentnum";

  /**
   * V7.0.0版本班级管理-获取单个学生动态
   *
   * @param clazzId  班级Id
   * @param userId   用户Id
   * @param pageNo   页码
   * @param pageSize 数量
   * @return
   */
  static const String getstudentdynamic =
      "/datacenter-api/api/datacenter/v50/dynamic/getstudentdynamic";

  /**
   * V7.0.0版本班级管理-获取班级学生动态
   *
   * @param clazzId  班级Id
   * @param userId   用户Id
   * @param pageNo   页码
   * @param pageSize 数量
   * @return
   */
  static const String getclazzstudentdynamic =
      "/datacenter-api/api/datacenter/v50/dynamic/getclazzstudentdynamic";

  /**
   * 6.0.1获取学习之星推荐数据
   *
   * @return
   */
  static const String exampleGetexample =
      "/user-api/api/yyb/v1/example/getexample";

  /**
     * 7.6.0英语整合单元列表
     *
     * @return
     */
  // @GET("/textbook-api/api/textbook/v50/english/unit/intg/queryintgunitlist")
  static const String queryintgunitlist =
      '/textbook-api/api/textbook/v50/english/unit/intg/queryintgunitlist';

  /**
   * 我的消息, 列表;
   */
  static const String queryNewsUserReceive =
      "/api/yyb/news/v3_6/queryNewsUserReceive";

  /**
   * 我的消息，删除(批量)
   */
  static const String deleteNewsUserReceive =
      "/api/yyb/news/v3_6/deleteNewsUserReceive";

  /**
   * 我的消息，已读(批量)
   */
  static const String updateReadStatus = "/api/yyb/news/v3_6/updateReadStatus";

  //823学生语数首页模块
  static const String onecmbookdata = "/api/textbook/v51/book/onecmbookdata";
  //823查询拓展页对应的阅读页数据
  static const String listexrdresource =
      "/api/interest/v50/dubbing/listexrdresource";
  //823查询最新置顶的一条短视频
  static const String onelateststvideo =
      "/api/course/v50/shortvideo/onelateststvideo";
  //823判断是否存在团购
  static const String checkstudentgroupbuy =
      "/api/shop/v50/groupbuy/checkstudentgroupbuy";


        /**
     * 查询签到配置
     *
     * @return
     */
    // @GET("/api/user/v50/sign/getsignconfig")
    static const String getsignconfig =
        "/api/user/v50/sign/getsignconfig";


         /**
   * 获取用户收藏资源
   *
   * @param puserId
   * @param pageSize
   * @param pageIndex
   * @return
   */
  static const String expGetUserCollectResource = "/studymodules-api/api/yyb/v1/expModule/api/getUserCollectResource";

  /**
   * 我收藏的课程列表
   *
   * @param pageNo 页码
   * @param pageSize 每页大小
   * @return
   */
  static const String queryCollectClass = "/studymodules-api/api/yyb/v1/expModule/api/findWeiClassListByUserId";

  /**
   * app查询收藏课时列表
   * @param userId 用户ID
   * @return
   */
  static const String queryUserCollection = "/course-api/api/yyb/v1/courseApi/queryUserCollection";

}
