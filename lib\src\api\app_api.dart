import 'package:dio/dio.dart';
import 'package:lib_base/model/http/ad_info.dart';
import 'package:lib_base/model/http/base_page_list_response_model.dart';
import 'package:lib_base/model/http/get_sign_config_model.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/dio.dart';
import 'package:lib_base/config/net/base_response.dart';
import 'package:lib_base/config/net/dio.dart';
import 'package:lib_base/config/net/interceptors/http_extra_key.dart';
import 'package:yyb_flutter/model/http/area_school_model.dart';
import 'package:lib_base/model/http/sys_config.dart';
import 'package:yyb_flutter/model/http/book_info_by_area_model.dart';
import 'package:lib_base/model/http/book_version_model.dart';
import 'package:yyb_flutter/model/http/chinese_model.dart';
import 'package:yyb_flutter/model/http/class_dynamic_response.dart';
import 'package:yyb_flutter/model/http/discover_data_model.dart';
import 'package:yyb_flutter/model/http/english_course_info.dart';
import 'package:yyb_flutter/model/http/english_interest_info.dart';
import 'package:yyb_flutter/model/http/english_recommend_activity.dart';
import 'package:yyb_flutter/model/http/english_short_video_info.dart';
import 'package:yyb_flutter/model/http/example_get_example_response.dart';
import 'package:yyb_flutter/model/http/expand_video_item.dart';
import 'package:yyb_flutter/model/http/explain_phone_model.dart';
import 'package:yyb_flutter/model/http/feedback_item_model.dart';
import 'package:yyb_flutter/model/http/glory_header_model.dart';
import 'package:yyb_flutter/model/http/head_info_model.dart';
import 'package:lib_base/model/id_name_info.dart';
import 'package:yyb_flutter/model/http/my_header_model.dart';
import 'package:yyb_flutter/model/http/news_user_receive_model.dart';
import 'package:yyb_flutter/model/http/order_campandju_model.dart';
import 'package:yyb_flutter/model/http/query_chinese_expand_model.dart';
import 'package:yyb_flutter/model/http/read_model.dart';
import 'package:yyb_flutter/model/http/student_dynamic_model.dart';
import 'package:yyb_flutter/model/http/update_pwd_model.dart';
import 'package:yyb_flutter/model/http/video_model.dart';
import 'package:yyb_flutter/model/http/collect_class_model.dart';
import 'package:yyb_flutter/model/http/word_course_user_collection_bean.dart';

import 'api.dart';

part 'app_api.g.dart';

@RestApi()
abstract class AppApi {
  static AppApi? _instance;

  factory AppApi.instance() {
    return AppApi();
  }

  factory AppApi() {
    _instance ??= _AppApi(dio);
    return _instance!;
  }

  @GET(Api.findByAppId)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: true,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<SysConfig>> findByAppId(
    @Query("version") String version,
    @Query("memberId") String memberId,
    @Query("appId") String appId,
  );

  @GET(Api.listavailables)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<AdInfo>>> listavailables(
    @Query("positionIds") List<String> positionIds,
    @Query("userLevelCode") String? userLevelCode,
    @Query("appInnerVersion") String? appInnerVersion,
    @Query("userId") String? userId,
  );

  // @GET(Api.queryadvertise)
  // @Extra(<String, bool>{
  //   HttpExtraKey.autoLoading: false,
  //   HttpExtraKey.withoutLogin: false,
  //   HttpExtraKey.needErrorToast: true,
  // })
  // Future<BaseResponse<List<AdvertiseInfo>>> queryadvertise(
  //   @Query("positionId") String? positionId,
  //   @Query("userLevelCode") String? userLevelCode,
  //   @Query("appInnerVersion") String? appInnerVersion,
  //   @Query("userId") String? userId,
  // );

  @GET(Api.queryEnglishCourse)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<EnglishCourseInfo>>> queryEnglishCourse(
    @Query("grade") String? grade,
    @Query("userLevel") String? userLevel,
    @Query("appMenu") String? appMenu,
    @Query("appId") String? appId,
  );

  @GET(Api.queryenglishinterestthree)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<EnglishInterestInfo>>> queryenglishinterestthree(
      @Query("grade") String? grade,
      @Query("userLevel") String? userLevel,
      @Query("appMenu") String? appMenu,
      @Query("appId") String? appId,
      @Query("userId") String? userId,
      @Query("versionNum") String? versionNum);

  @GET(Api.pageappshortvideodata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<EnglishShortVideoInfo>> pageappshortvideodata(
    @Query("qryType") int? qryType,
    @Query("userId") String? userId,
    @Query("pageNo") int? pageNo,
    @Query("pageSize") int? pageSize,
    @Query("name") String? name,
  );

  @GET(Api.getRecommendActivity)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<EnglishRecommendActivity>> getRecommendActivity();

  @GET(Api.selectshortvideoallseries)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<IdNameInfo>>> selectshortvideoallseries();

  @GET(Api.querychineseexpand)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<QueryChineseExpandModel>>> querychineseexpand(
      @Query("grade") String? grade,
      @Query("userLevel") String? userLevel,
      @Query("appMenu") String? appMenu,
      @Query("appId") String? appId,
      @Query("bookId") String? bookId,
      @Query("versionNum") String? versionNum);

  @GET(Api.getMyBestResult)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> getMyBestResult(@Query("userId") String? userId, @Query("activityId") String? activityId);

  @GET(Api.queryAreaSchool)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<AreaSchoolModel>>> queryAreaSchool(
    @Query("areaNames") String? areaNames,
  );

  @GET(Api.listbkversion)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<BookVersionModel>>> listbkversion(@Query("subject") String? subject,
      @Query("version") String? version, @Query("grade") String? grade, @Query("fascicule") String? fascicule);

  @GET(Api.querybooknominatebyarea)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<BookInfoByAreaModel>>> querybooknominatebyarea(@Query("subject") String? subject,
      @Query("areaCodes") String? areaCodes, @Query("grade") String? grade, @Query("fascicule") String? fascicule);

  @GET(Api.explainPhone)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ExplainPhoneModel>> explainPhone(
    @Query("type") String? type,
  );

  @POST(Api.addadvice)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> addadvice(
    @Field("puserId") String puserId,
    @Field("value") int issuevalue,
    @Field("content") String content,
    @Field("filePath") String? filePath,
    @Field("fileType") String? fileType,
    @Field("userPhone") String? userPhone,
    @Field("uinitId") String? uinitId,
    @Field("unitName") String? unitName,
    @Field("type") String? type,
    @Field("moduleId") String? moduleId,
    @Field("moduleName") String? moduleName,
    @Field("appId") String? appId,
    @Field("subject") String? subject,
    @Field("segment") String? segment,
    @Field("version") String? version,
    @Field("grade") String? grade,
    @Field("fascicule") String? fascicule,
  );

  @GET(Api.finduseradvices)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<BasePageListResponseModel<FeedbackItemModel>>> finduseradvices(
    @Query("puserId") String puserId,
    @Query("moduleId") String? moduleId,
    @Query("pageNo") int pageNo,
    @Query("pageSize") int pageSize,
  );

  @POST(Api.changePass)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<UpdatePwdModel>> changePass(
    @Field("oldPass") String oldPass,
    @Field("password") String password,
    @Field("resPassword") String resPassword,
  );

  @POST(Api.verifyNewPhone)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> verifyNewPhone(
    @Field("phone") String phone,
    @Field("verifyCode") String verifyCode,
  );

  @POST(Api.verifyOldPhone)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> verifyOldPhone(
    @Field("phone") String phone,
    @Field("verifyCode") String verifyCode,
  );

  @POST(Api.close)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> close(
    @Field("password") String password,
  );

  @GET(Api.discovereddata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<DiscoverDataModel>>> discovereddata(
    @Query("userId") String userId,
    @Query("grade") String grade,
  );

  @GET(Api.checkuserblend)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> checkuserblend(
    @Query("userId") String userId,
    @Query("blendId") String grade,
  );

  @GET(Api.listcampdata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<DisCoverItem>>> listcampdata(
    @Query("grade") String grade,
  );

  @POST(Api.applyjoinclazzstudentnumber)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse> applyjoinclazzstudentnumber(
    @Field("studentName") String studentName,
    @Field("clazzNo") String clazzNo,
    @Field("studentNumber") String? studentNumber,
  );

  @GET(Api.listmyhead)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<MyHeaderModel>>> listmyhead(@Query("photo") String? photo);

  @GET(Api.getheadinfo)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<HeadInfoModel>> getheadinfo(@Query("photo") String? photo, @Query("level") String? level);

  @GET(Api.gloryList)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<GloryHeaderModel>>> gloryList(
    @Query("photo") String? photo,
  );

  @POST(Api.usehead)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> usehead(
    @Field("appId") String? appId,
    @Field("headId") String? headId,
    @Field("glory") String? glory,
  );

  @POST(Api.redemptionhead)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> redemptionhead(
    @Field("appId") String? appId,
    @Field("headId") String? headId,
    @Field("level") String? level,
  );

  @GET(Api.listordercampandju)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<OrderCampandjuModel>>> listordercampandju(
    @Query("userId") String userId,
  );

  @POST(Api.upclazzstudentnum)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> upclazzstudentnum(
    @Field("userId") String userId,
    @Field("clazzId") String clazzId,
    @Field("isIdentity") String isIdentity,
    @Field("studentNumber") int? studentNumber,
  );

  @GET(Api.getstudentdynamic)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<StudentDynamicModel>> getstudentdynamic(
    @Query("userId") String userId,
    @Query("clazzId") String clazzId,
    @Query("pageNo") int pageNo,
    @Query("pageSize") int pageSize,
  );

  @GET(Api.getclazzstudentdynamic)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ClassDynamicResponse>> getclazzstudentdynamic(
    @Query("userId") String userId,
    @Query("clazzId") String clazzId,
    @Query("pageNo") int pageNo,
    @Query("pageSize") int pageSize,
  );

  @GET(Api.exampleGetexample)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<ExampleGetExampleResponse>>> exampleGetexample(
    @Query("province") String province,
    @Query("city") String city,
    @Query("address") String address,
  );

  //  @GET("/textbook-api/api/textbook/v50/english/unit/intg/queryintgunitlist")
  // Call<JSONObject> queryintgunitlist(@Query("bookId") String bookId);
  @GET(Api.queryintgunitlist)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<IdNameInfo>>> queryintgunitlist(
    @Query("bookId") String bookId,
  );

  @GET(Api.queryNewsUserReceive)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<BasePageListResponseModel<NewsUserReceiveModel>>> queryNewsUserReceive(
      @Query("pageNo") int pageNo, @Query("pageSize") int pageSize);

  @POST(Api.deleteNewsUserReceive)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> deleteNewsUserReceive(
    @Field("newsUserReceiveIds") List<String> ids,
    @Field("text") String? newId,
  );

  @POST(Api.updateReadStatus)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> updateReadStatus(
    @Field("newsUserReceiveIds") List<String> ids,
    @Field("text") String? newId,
  );

  @GET(Api.onecmbookdata)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<ChineseModel>> onecmbookdata(
    @Query("id") String id,
    @Query("versionNum") String versionNum,
    @Query("userId") String userId,
  );

  @GET(Api.listexrdresource)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<List<ReadModel>>> listexrdresource(
    @Query("grade") String grade,
  );

  @GET(Api.onelateststvideo)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse<VideoModel>> onelateststvideo();

  @GET(Api.checkstudentgroupbuy)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: false,
  })
  Future<BaseResponse> checkstudentgroupbuy(
    @Query("userId") String userId,
    @Query("memberId") String memberId,
    @Query("areaCodes") String areaCodes,
    @Query("enVersion") String enVersion,
  );

  
    @GET(Api.getsignconfig)
  @Extra(<String, Object>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<GetSignConfigModel>> getsignconfig();


  
  /**
   * 获取用户收藏资源
   */
  @GET(Api.expGetUserCollectResource)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<BasePageListResponseModel<ExpandVideoItem>>> expGetUserCollectResource(
    @Query("puserId") String? puserId,
    @Query("pageSize") int? pageSize,
    @Query("pageIndex") int? pageIndex,
  );

  /**
   * 我收藏的课程列表
   */
  @GET(Api.queryCollectClass)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<BasePageListResponseModel<CollectClassModel>>> queryCollectClass(
    @Query("pageNo") int pageIndex,
    @Query("pageSize") int pageSize,
  );

  /**
   * app查询收藏课时列表
   */
  @GET(Api.queryUserCollection)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<WordCourseUserCollectionBean>>> queryUserCollection(
    @Query("userId") String userId,
  );
}
