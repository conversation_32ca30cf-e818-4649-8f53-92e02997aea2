// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collect_word_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CollectWordBean _$CollectWordBeanFromJson(Map<String, dynamic> json) =>
    CollectWordBean(
      id: json['id'] as String?,
      expModuleId: json['expModuleId'] as String?,
      type: json['type'] as String?,
      obj: json['obj'] == null
          ? null
          : WordDetailBean.fromJson(json['obj'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CollectWordBeanToJson(CollectWordBean instance) =>
    <String, dynamic>{
      'id': instance.id,
      'expModuleId': instance.expModuleId,
      'type': instance.type,
      'obj': instance.obj,
    };
