// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word_course_user_collection_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WordCourseUserCollectionBean _$WordCourseUserCollectionBeanFromJson(
        Map<String, dynamic> json) =>
    WordCourseUserCollectionBean(
      id: json['id'] as String?,
      courseId: json['courseId'] as String?,
      zhName: json['zhName'] as String?,
      enName: json['enName'] as String?,
      sort: json['sort'] as num?,
      isFree: json['isFree'] as String?,
      freeTime: json['freeTime'] as String?,
      image: json['image'] as String?,
      knowledgeNum: json['knowledgeNum'] as num?,
    );

Map<String, dynamic> _$WordCourseUserCollectionBeanToJson(
        WordCourseUserCollectionBean instance) =>
    <String, dynamic>{
      'id': instance.id,
      'courseId': instance.courseId,
      'zhName': instance.zhName,
      'enName': instance.enName,
      'sort': instance.sort,
      'isFree': instance.isFree,
      'freeTime': instance.freeTime,
      'image': instance.image,
      'knowledgeNum': instance.knowledgeNum,
    };
