import 'package:json_annotation/json_annotation.dart';

part 'word_course_user_collection_bean.g.dart';

@JsonSerializable()
class WordCourseUserCollectionBean {
  final String? id;
  final String? courseId;
  final String? zhName;
  final String? enName;
  final num? sort;
  final String? isFree;
  final String? freeTime;
  final String? image;
  final num? knowledgeNum;

  const WordCourseUserCollectionBean({
    this.id,
    this.courseId,
    this.zhName,
    this.enName,
    this.sort,
    this.isFree,
    this.freeTime,
    this.image,
    this.knowledgeNum,
  });

  factory WordCourseUserCollectionBean.fromJson(Map<String, dynamic> json) =>
      _$WordCourseUserCollectionBeanFromJson(json);

  Map<String, dynamic> toJson() => _$WordCourseUserCollectionBeanToJson(this);
}
