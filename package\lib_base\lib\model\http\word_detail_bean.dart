import 'package:json_annotation/json_annotation.dart';

part 'word_detail_bean.g.dart';

@JsonSerializable()
class WordDetailBean {
  String? id;
  String? name;
  String? paraphrase;
  String? audioFile;
  String? wordId;
  String? meaningNames;
  String? filePath;
  num? sort;
  num? appStatus;
  String? isShow;
  String? sourceType;
  String? releaseState;

  WordDetailBean({
    this.id,
    this.name,
    this.paraphrase,
    this.audioFile,
    this.wordId,
    this.meaningNames,
    this.filePath,
    this.sort,
    this.appStatus,
    this.isShow,
    this.sourceType,
    this.releaseState,
  });

  factory WordDetailBean.fromJson(Map<String, dynamic> json) =>
      _$WordDetailBeanFromJson(json);

  Map<String, dynamic> toJson() => _$WordDetailBeanToJson(this);
}
